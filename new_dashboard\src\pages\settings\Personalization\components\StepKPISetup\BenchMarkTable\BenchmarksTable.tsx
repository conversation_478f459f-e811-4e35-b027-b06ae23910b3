"use client";
import { useState } from "react";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Copy } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import TableBodyRow from "./TableBodyRow";
import { KPIBenchmark } from "../../../utils/types";

interface BenchmarksTableProps {
  channelId: string;
  benchmarks: KPIBenchmark[];
  onChange: (benchmarks: KPIBenchmark[]) => void;
}

const BenchmarksTable = ({ channelId, benchmarks, onChange }: BenchmarksTableProps) => {
  const headersByChannel: Record<string, string[]> = {
    google: ["Month", "Spend", "Conversions", "CPC Target", "CTR Target"],
    meta: ["Month", "Impressions", "Reach", "CPM", "CTR"],
    shopify: ["Month", "Revenue", "Orders", "AOV", "Conversion Rate"],
  };

  const [extraColumns, setExtraColumns] = useState<string[]>([]);
  const availableKPIs = ["ROAS", "CPA", "Engagement Rate", "Video Views", "Leads"];

  const headers = [...(headersByChannel[channelId] ?? []), ...extraColumns];

  const addMonth = () => {
    onChange([...benchmarks, { month: "", spend: 0 }]);
  };

  const replicateCurrentMonth = () => {
    if (benchmarks.length === 0) return;
    const last = benchmarks[benchmarks.length - 1];
    const newMonth = { ...last, month: "" };
    onChange([...benchmarks, newMonth]);
  };

  const handleCellChange = (i: number, key: string, value: string) => {
    const updated = [...benchmarks];
    updated[i] = { ...benchmarks[i], [key.toLowerCase().replace(/\s/g, "")]: value };
    onChange(updated);
  };

  const handleDeleteRow = (i: number) => {
    const updated = benchmarks.filter((_, idx) => idx !== i);
    onChange(updated);
  };

  const handleAddColumn = (newColumn: string) => {
    if (!extraColumns.includes(newColumn)) setExtraColumns([...extraColumns, newColumn]);
  };

  return (
    <div className="rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm overflow-hidden bg-white dark:bg-neutral-900 transition-all">
      <Table>
        <TableHeader>
          <TableRow className="bg-gray-50 dark:bg-neutral-800/60">
            {headers.map((h) => (
              <TableHead key={h} className="text-gray-700 dark:text-gray-300 font-medium">
                {h}
              </TableHead>
            ))}
            <TableHead className="text-gray-700 dark:text-gray-300">Actions</TableHead>
            <TableHead className="w-[180px]">
              <Select onValueChange={handleAddColumn}>
                <SelectTrigger className="h-8 text-sm border-dashed border-gray-400 dark:border-gray-600">
                  <SelectValue placeholder="+ Add Column" />
                </SelectTrigger>
                <SelectContent className="max-h-[180px] overflow-y-auto bg-white dark:bg-neutral-900">
                  {availableKPIs.map((kpi) => (
                    <SelectItem key={kpi} value={kpi}>
                      {kpi}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {benchmarks.length > 0 ? (
            benchmarks.map((row, i) => (
              <TableBodyRow
                key={i}
                row={row}
                headers={headers}
                index={i}
                onChange={handleCellChange}
                onDelete={handleDeleteRow}
              />
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={headers.length + 2} className="text-center text-gray-500 text-sm py-6">
                No benchmark data available
              </TableCell>
            </TableRow>
          )}

          <TableRow>
            <TableCell colSpan={headers.length + 2} className="text-center py-4 bg-gray-50 dark:bg-neutral-800/60">
              <div className="flex justify-center gap-3">
                <Button
                  onClick={addMonth}
                  variant="ghost"
                  size="sm"
                  className="text-[#7F56D9] hover:bg-[#7F56D9]/10 flex items-center gap-1"
                >
                  <Plus className="h-4 w-4" />
                  Add Month
                </Button>
                <Button
                  onClick={replicateCurrentMonth}
                  variant="ghost"
                  size="sm"
                  className="text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/40 flex items-center gap-1"
                >
                  <Copy className="h-4 w-4" />
                  Replicate Current Month
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};

export default BenchmarksTable;
